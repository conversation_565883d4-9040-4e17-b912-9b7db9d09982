# Security Policy

## Reporting a Vulnerability

We take the security of NativeMind seriously. If you believe you've found a security vulnerability in our code, please follow these steps:

1. **Do not disclose the vulnerability publicly**, including filing a public issue on GitHub.
2. Email us at [<EMAIL>](mailto:<EMAIL>) with details about the vulnerability.
3. Include the following information in your report:
   - Type of vulnerability
   - Full paths of affected files
   - Location in code (line numbers)
   - Any special configuration required to reproduce
   - Step-by-step instructions to reproduce the vulnerability
   - Proof of concept or exploit code (if possible)
   - Impact of the vulnerability

## What to expect

After you report a vulnerability:

- We will acknowledge receipt of your vulnerability report within 48 hours.
- We will provide a more detailed response within 7 days with our assessment of the vulnerability and an estimated timeframe for a fix.
- We will keep you updated as we work on a fix.
- We will notify you when the vulnerability has been fixed.

## Security Updates

Security updates will be released as part of our regular release cycle, unless a critical vulnerability requires an immediate patch.

## Bug Bounty Program

We currently do not offer a bug bounty program for security vulnerabilities.

## Thank You

We value the security community and responsible disclosure of security issues. We acknowledge contributors who report valid security vulnerabilities in our Security Acknowledgments.
