# Contributing to NativeMind

First off, thank you for considering contributing to NativeMind! It's people like you that make NativeMind such a great tool.

## Code of Conduct

By participating in this project, you are expected to uphold our Code of Conduct. Please report unacceptable behavior to [<EMAIL>].

## How Can I Contribute?

### Reporting Bugs

This section guides you through submitting a bug report for NativeMind. Following these guidelines helps maintainers and the community understand your report, reproduce the behavior, and find related reports.

**Before Submitting A Bug Report:**

* Check the debugging guide for tips — you might discover that the problem is not a bug in the extension.
* Check if you can reproduce the problem in the latest version of NativeMind.
* Perform a search to see if the problem has already been reported.

**How Do I Submit A (Good) Bug Report?**

Bugs are tracked as GitHub issues. Create an issue and provide the following information:

* Use a clear and descriptive title.
* Describe the exact steps to reproduce the problem with as many details as possible.
* Provide specific examples to demonstrate the steps.
* Describe the behavior you observed after following the steps and why this is a problem.
* Explain which behavior you expected to see instead and why.
* Include screenshots if possible.
* If the problem is related to performance or memory, include browser console logs.

### Suggesting Enhancements

This section guides you through submitting an enhancement suggestion for NativeMind, including completely new features and minor improvements to existing functionality.

**Before Submitting An Enhancement Suggestion:**

* Check if the enhancement has already been suggested.
* Determine which repository the enhancement should be suggested in.

**How Do I Submit A (Good) Enhancement Suggestion?**

Enhancement suggestions are tracked as GitHub issues. Create an issue on the repository and provide the following information:

* Use a clear and descriptive title.
* Provide a step-by-step description of the suggested enhancement.
* Provide specific examples to demonstrate the enhancement.
* Describe the current behavior and explain which behavior you expected to see instead and why.
* Explain why this enhancement would be useful to most NativeMind users.

### Pull Requests

* Fill in the required template
* Do not include issue numbers in the PR title
* Include screenshots and animated GIFs in your pull request whenever possible
* Follow the style guides
* Include thoughtfully-worded, well-structured tests
* Document new code
* End all files with a newline

## Styleguides

### Git Commit Messages

* Use the present tense ("Add feature" not "Added feature")
* Use the imperative mood ("Move cursor to..." not "Moves cursor to...")
* Limit the first line to 72 characters or less
* Reference issues and pull requests liberally after the first line
* Consider starting the commit message with an applicable emoji:
  * 🎨 `:art:` when improving the format/structure of the code
  * 🐎 `:racehorse:` when improving performance
  * 📝 `:memo:` when writing docs
  * 🐛 `:bug:` when fixing a bug
  * 🔥 `:fire:` when removing code or files
  * ✅ `:white_check_mark:` when adding tests

### JavaScript/TypeScript Styleguide

* Use semicolons
* 2 spaces for indentation
* Prefer `const` over `let`. Never use `var`
* Prefer template literals over string concatenation
* Use camelCase for variables, properties and function names
* Use PascalCase for classes and Vue components
* Use UPPERCASE for constants

### Vue Styleguide

* Follow the [Vue Style Guide](https://vuejs.org/style-guide/)
* Use Single-File Components
* Component names should be multi-word
* Prop definitions should be as detailed as possible
* Always use key with v-for
* Use Composition API with `<script setup>`

### CSS Styleguide

* Follow TailwindCSS conventions

## Additional Notes

### Issue and Pull Request Labels

This section lists the labels we use to help us track and manage issues and pull requests.

* `bug` - Issues that are bugs
* `documentation` - Issues or PRs related to documentation
* `enhancement` - Issues that are feature requests
* `help wanted` - Issues that need assistance
* `good first issue` - Issues which are good for newcomers

## Development Setup

### Prerequisites

* Node.js (v22.14.0 or later)
* PNPM (v10.10.0 or later)

### Local Development

1. Fork the repository
2. Clone your fork
3. Create a new branch
4. Install dependencies with `pnpm install`
5. Start development server with `pnpm dev`
6. Make your changes
7. Test your changes
8. Commit your changes
9. Push your branch
10. Open a Pull Request

Thank you for your contributions!
