@import "tailwindcss";

@theme {
  --ease-cubic-1: cubic-bezier(0.175, 0.75, 0.19, 1.015);
  --font-system: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI",
    Roboto, "Helvetica Neue", Arial, sans-serif;

  --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
    "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
    "Liberation Mono", "Courier New", monospace;

  --color-black: #000;
  --color-white: #fff;

  --spacing-base: 16px;

  --spacing: calc(0.25 * var(--spacing-base));

  --breakpoint-sm: calc(40 * var(--spacing-base));
  --breakpoint-md: calc(48 * var(--spacing-base));
  --breakpoint-lg: calc(64 * var(--spacing-base));
  --breakpoint-xl: calc(80 * var(--spacing-base));
  --breakpoint-2xl: calc(96 * var(--spacing-base));

  --container-3xs: calc(16 * var(--spacing-base));
  --container-2xs: calc(18 * var(--spacing-base));
  --container-xs: calc(20 * var(--spacing-base));
  --container-sm: calc(24 * var(--spacing-base));
  --container-md: calc(28 * var(--spacing-base));
  --container-lg: calc(32 * var(--spacing-base));
  --container-xl: calc(36 * var(--spacing-base));
  --container-2xl: calc(42 * var(--spacing-base));
  --container-3xl: calc(48 * var(--spacing-base));
  --container-4xl: calc(56 * var(--spacing-base));
  --container-5xl: calc(64 * var(--spacing-base));
  --container-6xl: calc(72 * var(--spacing-base));
  --container-7xl: calc(80 * var(--spacing-base));

  --text-xs: calc(0.75 * var(--spacing-base));
  --text-sm: calc(0.875 * var(--spacing-base));
  --text-base: calc(1 * var(--spacing-base));
  --text-lg: calc(1.125 * var(--spacing-base));
  --text-xl: calc(1.25 * var(--spacing-base));
  --text-2xl: calc(1.5 * var(--spacing-base));
  --text-3xl: calc(1.875 * var(--spacing-base));
  --text-4xl: calc(2.25 * var(--spacing-base));
  --text-5xl: calc(3 * var(--spacing-base));
  --text-6xl: calc(3.75 * var(--spacing-base));
  --text-7xl: calc(4.5 * var(--spacing-base));
  --text-8xl: calc(6 * var(--spacing-base));
  --text-9xl: calc(8 * var(--spacing-base));

  --tracking-tighter: calc(-0.05 * var(--spacing-base));
  --tracking-tight: calc(-0.025 * var(--spacing-base));
  --tracking-normal: 0em;
  --tracking-wide: calc(0.025 * var(--spacing-base));
  --tracking-wider: calc(0.05 * var(--spacing-base));
  --tracking-widest: calc(0.1 * var(--spacing-base));

  --radius-xs: calc(0.125 * var(--spacing-base));
  --radius-sm: calc(0.25 * var(--spacing-base));
  --radius-md: calc(0.375 * var(--spacing-base));
  --radius-lg: calc(0.5 * var(--spacing-base));
  --radius-xl: calc(0.75 * var(--spacing-base));
  --radius-2xl: calc(1 * var(--spacing-base));
  --radius-3xl: calc(1.5 * var(--spacing-base));
  --radius-4xl: calc(2 * var(--spacing-base));

  --font-inter: "Inter", ui-sans-serif, system-ui, sans-serif;

  /* Colors only used in UI component should not be used in business component */
  --color-text-primary: #1f2326;
  --color-text-secondary: #596066;
  --color-text-tertiary: #747a80;
  --color-text-placeholder: #aeb5bd;
  --color-text-disabled: #aeb5bd;
  --color-bg-component: hsla(0, 0%, 98%, 1);

  --shadow-01: 0px 8px 16px 0px #00000014, 0px 4px 8px 0px #00000014,
    0px 0px 0px 1px #00000014;
  --shadow-02: 0px 0px 0px 1px #00000014, 0px 1px 2px 0px #0000001f;
}

.nativemind-style-boundary .scrollbar-hide {
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
}

.nativemind-style-boundary .scrollbar-hide::-webkit-scrollbar {
  display: none;
  /* Chrome, Safari and Opera */
}

.nativemind-style-boundary *::-webkit-scrollbar {
  width: 5px;
  height: 5px;
  background-color: transparent;
  /* or add it to the track */
}

.nativemind-style-boundary *::-webkit-scrollbar-thumb {
  background: #e4e4e7;
  border-radius: 5px;
}
