name: Release Version

run-name: 'Release ${{ inputs.releaseType }}'

on:
  workflow_dispatch:
    inputs:
      releaseType:
        description: 'Release type'
        required: true
        default: 'beta'
        type: choice
        options:
          - beta
          - prod

jobs:
  release:
    name: Release ${{ inputs.releaseType }}
    runs-on: ubuntu-latest
    environment: release
    permissions:
      contents: write
      pull-requests: write

    outputs:
      new_version: ${{ steps.get-version.outputs.version }}

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ssh-key: ${{ secrets.DEPLOY_KEY }}

      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          run_install: false

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
          cache: 'pnpm'

      - name: Configure Git
        run: |
          git config --global user.name 'github-actions[bot]'
          git config --global user.email 'github-actions[bot]@users.noreply.github.com'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run release script
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: pnpm run release:${{ inputs.releaseType }}

      - name: Get new version
        id: get-version
        run: |
          VERSION=$(node -p "require('./package.json').version")
          echo "version=$VERSION" >> "$GITHUB_OUTPUT"
          echo "Released version: $VERSION" >> $GITHUB_STEP_SUMMARY
          echo "RELEASE_VERSION=$VERSION" >> $GITHUB_ENV
