name: Security Scan

on:
  schedule:
    - cron: "0 0 * * 1"
  push:
    branches: [main]
    paths:
      - "package.json"
      - "pnpm-lock.yaml"
  workflow_dispatch:

jobs:
  security-scan:
    name: Security Vulnerability Scan
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          run_install: false

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
          cache: 'pnpm'

      - name: Run npm audit
        run: pnpm audit --production
        continue-on-error: true

      - name: Check for outdated dependencies
        run: pnpm outdated
        continue-on-error: true
