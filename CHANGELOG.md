# Changelog


## v1.2.0

[compare changes](https://github.com/NativeMindBrowser/NativeMindExtension/compare/v1.2.0-beta.16...v1.2.0)

## v1.2.0-beta.16

[compare changes](https://github.com/NativeMindBrowser/NativeMindExtension/compare/v1.2.0-beta.15...v1.2.0-beta.16)

### 🚀 Enhancements

- **background:** Inject content script on installation ([a9b410d](https://github.com/NativeMindBrowser/NativeMindExtension/commit/a9b410d))

### ❤️ Contributors

- <PERSON> Hu ([@tonyhu-012](http://github.com/tonyhu-012))

## v1.2.0-beta.15

[compare changes](https://github.com/NativeMindBrowser/NativeMindExtension/compare/v1.2.0-beta.14...v1.2.0-beta.15)

### 🚀 Enhancements

- **wxt:** Add module to expose web resources and update config ([93c5d03](https://github.com/NativeMindBrowser/NativeMindExtension/commit/93c5d03))

### 🩹 Fixes

- **styles:** Enhance style injection and loading mechanism for shadow DOM ([e947697](https://github.com/NativeMindBrowser/NativeMindExtension/commit/e947697))

### ❤️ Contributors

- Tony Hu ([@tonyhu-012](http://github.com/tonyhu-012))

## v1.2.0-beta.14

[compare changes](https://github.com/NativeMindBrowser/NativeMindExtension/compare/v1.2.0-beta.13...v1.2.0-beta.14)

### 🩹 Fixes

- **translator:** Ensure translation menu updates only when document is visible and improve context menu handling ([2f8ad72](https://github.com/NativeMindBrowser/NativeMindExtension/commit/2f8ad72))

### ❤️ Contributors

- Tony Hu ([@tonyhu-012](http://github.com/tonyhu-012))

## v1.2.0-beta.13

[compare changes](https://github.com/NativeMindBrowser/NativeMindExtension/compare/v1.2.0-beta.12...v1.2.0-beta.13)

### 🩹 Fixes

- **translator:** Prevent connection attempt to Ollama when translation is disabled ([9b7943f](https://github.com/NativeMindBrowser/NativeMindExtension/commit/9b7943f))

### ❤️ Contributors

- Tony Hu ([@tonyhu-012](http://github.com/tonyhu-012))

## v1.2.0-beta.12

[compare changes](https://github.com/NativeMindBrowser/NativeMindExtension/compare/v1.2.0-beta.11...v1.2.0-beta.12)

### 🩹 Fixes

- **popup:** Show reload message in popup on unattached tabs ([3e4f0f8](https://github.com/NativeMindBrowser/NativeMindExtension/commit/3e4f0f8))

### ❤️ Contributors

- Tony Hu ([@tonyhu-012](http://github.com/tonyhu-012))

## v1.2.0-beta.11

[compare changes](https://github.com/NativeMindBrowser/NativeMindExtension/compare/v1.2.0-beta.10...v1.2.0-beta.11)

### 🚀 Enhancements

- **context-menu:** Refactor context menu items and update translation menu on tab activation ([d23dea6](https://github.com/NativeMindBrowser/NativeMindExtension/commit/d23dea6))

### ❤️ Contributors

- Tony Hu ([@tonyhu-012](http://github.com/tonyhu-012))

## v1.2.0-beta.10

[compare changes](https://github.com/NativeMindBrowser/NativeMindExtension/compare/v1.2.0-beta.9...v1.2.0-beta.10)

### 🚀 Enhancements

- **chat:** Send button style ([243eae7](https://github.com/NativeMindBrowser/NativeMindExtension/commit/243eae7))

### ❤️ Contributors

- Tony Hu ([@tonyhu-012](http://github.com/tonyhu-012))

## v1.2.0-beta.9

[compare changes](https://github.com/NativeMindBrowser/NativeMindExtension/compare/v1.2.0-beta.8...v1.2.0-beta.9)

### 🩹 Fixes

- Translation context menu ([9eb62a1](https://github.com/NativeMindBrowser/NativeMindExtension/commit/9eb62a1))

### ❤️ Contributors

- Tony Hu ([@tonyhu-012](http://github.com/tonyhu-012))

## v1.2.0-beta.8

[compare changes](https://github.com/NativeMindBrowser/NativeMindExtension/compare/v1.2.0-beta.7...v1.2.0-beta.8)

### 🏡 Chore

- **release:** Add environment specification for release job ([efc7e4d](https://github.com/NativeMindBrowser/NativeMindExtension/commit/efc7e4d))

### ❤️ Contributors

- Tony Hu ([@tonyhu-012](http://github.com/tonyhu-012))

## v1.2.0-beta.7

[compare changes](https://github.com/NativeMindBrowser/NativeMindExtension/compare/v1.2.0-beta.6...v1.2.0-beta.7)

### 🩹 Fixes

- Show settings if ollama not available when using translation ([9d1bcf5](https://github.com/NativeMindBrowser/NativeMindExtension/commit/9d1bcf5))

### ❤️ Contributors

- Tony Hu ([@tonyhu-012](http://github.com/tonyhu-012))

## v1.2.0-beta.6

[compare changes](https://github.com/NativeMindBrowser/NativeMindExtension/compare/v1.2.0-beta.5...v1.2.0-beta.6)

### 🚀 Enhancements

- **docs:** Add links to official website in README ([85dfca3](https://github.com/NativeMindBrowser/NativeMindExtension/commit/85dfca3))
- **translator:** improve text content formatting in prompts and add skip target selector
- **translator:** Some pages become unresponsive when using the translation ([7d60f69](https://github.com/NativeMindBrowser/NativeMindExtension/commit/7d60f69))

### 🩹 Fixes

- **readme:** Update demo screenshot ([944ae52](https://github.com/NativeMindBrowser/NativeMindExtension/commit/944ae52))

### ❤️ Contributors

- Tony Hu ([@tonyhu-012](http://github.com/tonyhu-012))
- Xukecheng <<EMAIL>>

## v1.2.0-beta.5

[compare changes](https://github.com/NativeMindBrowser/NativeMindExtension/compare/v1.2.0-beta.4...v1.2.0-beta.5)

### 🚀 Enhancements

- **chat:** enhance send button positioning and styling in chat component chore
- **i18n:** Update extension description for clarity and accuracy ([d49c3bc](https://github.com/NativeMindBrowser/NativeMindExtension/commit/d49c3bc))

### ❤️ Contributors

- Tony Hu ([@tonyhu-012](http://github.com/tonyhu-012))

## v1.2.0-beta.4

[compare changes](https://github.com/NativeMindBrowser/NativeMindExtension/compare/v1.2.0-beta.3...v1.2.0-beta.4)

### 🚀 Enhancements

- **style:** Update ModelSelector container position ([1ddfcba](https://github.com/NativeMindBrowser/NativeMindExtension/commit/1ddfcba))

### ❤️ Contributors

- Tony Hu ([@tonyhu-012](http://github.com/tonyhu-012))

## v1.2.0-beta.3

[compare changes](https://github.com/NativeMindBrowser/NativeMindExtension/compare/v1.2.0-beta.2...v1.2.0-beta.3)

### 🩹 Fixes

- Implement URL validation and timeout handling for url like chrome webstore ([8e4726a](https://github.com/NativeMindBrowser/NativeMindExtension/commit/8e4726a))
- Adjust textarea height handling and update tab selection order ([441d2d0](https://github.com/NativeMindBrowser/NativeMindExtension/commit/441d2d0))

### 💅 Refactors

- **auto-imports:** Disable auto imports ([8bb6192](https://github.com/NativeMindBrowser/NativeMindExtension/commit/8bb6192))
- **lint:** Update eslint rules ([eda482f](https://github.com/NativeMindBrowser/NativeMindExtension/commit/eda482f))

### ❤️ Contributors

- Tony Hu ([@tonyhu-012](http://github.com/tonyhu-012))

## v1.2.0-beta.2

[compare changes](https://github.com/NativeMindBrowser/NativeMindExtension/compare/v1.2.0-beta.1...v1.2.0-beta.2)

## v1.2.0-beta.1


### 🚀 Enhancements

- Welcome to NativeMind ([e6f9f30](https://github.com/NativeMindBrowser/NativeMindExtension/commit/e6f9f30))

### ❤️ Contributors

- Tony Hu ([@tonyhu-012](http://github.com/tonyhu-012))

## v1.2.0

- 🚀 Welcome to NativeMind!
